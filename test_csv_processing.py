#!/usr/bin/env python3
"""
CSV风险预测测试脚本
演示CSV处理相比Excel的优势
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.csv_risk_prediction_api import CSVRiskPredictionAPI
import pandas as pd
import numpy as np
from datetime import datetime
import time


def create_sample_csv():
    """创建示例CSV文件用于测试"""
    print("🔧 创建示例CSV文件...")
    
    # 生成示例风控数据
    np.random.seed(42)
    n_samples = 500  # 500行数据
    
    data = {
        '客户ID': [f'CUST_{i:06d}' for i in range(1, n_samples + 1)],
        '年龄': np.random.randint(18, 70, n_samples),
        '月收入': np.random.normal(8000, 3000, n_samples).astype(int),
        '工作年限': np.random.randint(0, 30, n_samples),
        '信用评分': np.random.randint(300, 850, n_samples),
        '负债总额': np.random.normal(50000, 30000, n_samples).astype(int),
        '房产价值': np.random.normal(200000, 100000, n_samples).astype(int),
        '教育程度': np.random.choice(['高中', '大专', '本科', '硕士', '博士'], n_samples),
        '婚姻状况': np.random.choice(['未婚', '已婚', '离异'], n_samples),
        '职业类型': np.random.choice(['公务员', '企业员工', '自由职业', '个体户', '退休'], n_samples),
        '逾期次数': np.random.poisson(1, n_samples),
        '信用卡数量': np.random.randint(0, 8, n_samples),
        '贷款申请金额': np.random.normal(100000, 50000, n_samples).astype(int),
        '还款能力评级': np.random.choice(['A', 'B', 'C', 'D'], n_samples, p=[0.3, 0.4, 0.2, 0.1])
    }
    
    df = pd.DataFrame(data)
    
    # 确保数据目录存在
    os.makedirs('data/csv', exist_ok=True)
    csv_path = 'data/csv/sample_risk_data.csv'
    
    df.to_csv(csv_path, index=False, encoding='utf-8')
    print(f"✅ 示例CSV文件已创建: {csv_path}")
    print(f"   数据规模: {len(df)} 行 x {len(df.columns)} 列")
    
    return csv_path


def test_csv_processing():
    """测试CSV处理功能"""
    print("\n" + "="*60)
    print(" CSV风险预测测试 ")
    print("="*60)
    
    # 创建示例CSV文件
    csv_path = create_sample_csv()
    
    # 初始化API
    print("\n🚀 初始化CSV风险预测API...")
    api = CSVRiskPredictionAPI(rows_per_chunk=50, chunk_overlap_rows=5)  # 每块50行
    
    # 上传CSV文件
    print(f"\n📤 上传CSV文件: {csv_path}")
    start_time = time.time()
    
    upload_result = api.upload_csv_and_get_code(csv_path)
    
    upload_time = time.time() - start_time
    
    if upload_result.get("success"):
        file_code = upload_result["file_code"]
        print(f"✅ 上传成功!")
        print(f"   文件编码: {file_code}")
        print(f"   文档数量: {upload_result['document_count']}")
        print(f"   文件大小: {upload_result['file_size']} 字节")
        print(f"   上传耗时: {upload_time:.2f} 秒")
        
        # 显示文件分析信息
        analysis = upload_result["validation"]["analysis"]
        print(f"\n📊 文件分析:")
        print(f"   总行数: {analysis['total_rows']}")
        print(f"   总列数: {analysis['total_columns']}")
        print(f"   预计文档块数: {analysis['estimated_chunks']}")
        print(f"   数值列: {len(analysis['numeric_columns'])} 个")
        print(f"   文本列: {len(analysis['text_columns'])} 个")
        
        return file_code
    else:
        print(f"❌ 上传失败: {upload_result.get('error')}")
        return None


def test_risk_prediction(file_code):
    """测试风险预测功能"""
    if not file_code:
        return
    
    print("\n" + "="*60)
    print(" 风险预测测试 ")
    print("="*60)
    
    api = CSVRiskPredictionAPI()
    
    # 测试智能选择策略
    print("\n🧠 测试智能选择策略...")
    start_time = time.time()
    
    smart_result = api.predict_by_file_code(file_code, use_batch_analysis=False)
    
    smart_time = time.time() - start_time
    
    if smart_result.get("success"):
        print(f"✅ 智能选择分析完成!")
        print(f"   违约概率: {smart_result.get('default_probability', 0):.2%}")
        print(f"   风险等级: {smart_result.get('risk_level', 'N/A')}")
        print(f"   分析耗时: {smart_time:.2f} 秒")
    else:
        print(f"❌ 智能选择分析失败: {smart_result.get('error')}")
    
    # 测试多批次策略
    print("\n🔄 测试多批次分析策略...")
    start_time = time.time()
    
    batch_result = api.predict_by_file_code(file_code, use_batch_analysis=True)
    
    batch_time = time.time() - start_time
    
    if batch_result.get("success"):
        print(f"✅ 多批次分析完成!")
        print(f"   违约概率: {batch_result.get('default_probability', 0):.2%}")
        print(f"   风险等级: {batch_result.get('risk_level', 'N/A')}")
        print(f"   分析批次: {batch_result.get('total_batches', 'N/A')}")
        print(f"   分析耗时: {batch_time:.2f} 秒")
    else:
        print(f"❌ 多批次分析失败: {batch_result.get('error')}")
    
    # 策略比较
    if smart_result.get("success") and batch_result.get("success"):
        print("\n📈 策略比较:")
        smart_prob = smart_result.get('default_probability', 0)
        batch_prob = batch_result.get('default_probability', 0)
        
        print(f"   智能选择: {smart_prob:.2%} ({smart_time:.2f}秒)")
        print(f"   多批次: {batch_prob:.2%} ({batch_time:.2f}秒)")
        print(f"   概率差异: {abs(smart_prob - batch_prob):.2%}")
        print(f"   速度比较: 智能选择快 {batch_time/smart_time:.1f} 倍")
        
        if abs(smart_prob - batch_prob) < 0.05:
            print("   🎯 两种策略结果高度一致")
        elif abs(smart_prob - batch_prob) < 0.15:
            print("   ✅ 两种策略结果基本一致")
        else:
            print("   ⚠️ 两种策略结果存在差异")


def compare_with_excel_processing():
    """比较CSV和Excel处理的差异"""
    print("\n" + "="*60)
    print(" CSV vs Excel 处理对比 ")
    print("="*60)
    
    print("\n📊 理论对比分析:")
    print("┌─────────────────┬─────────────────┬─────────────────┐")
    print("│     指标        │    Excel处理    │    CSV处理      │")
    print("├─────────────────┼─────────────────┼─────────────────┤")
    print("│ 文档分块策略    │ 按字符数分块    │ 按行数分块      │")
    print("│ 分块大小        │ 3000字符/块     │ 50行/块         │")
    print("│ 数据重复        │ 有(工作表+报告) │ 无              │")
    print("│ 处理复杂度      │ 高(Excel解析)   │ 低(纯文本)      │")
    print("│ 内存占用        │ 较高            │ 较低            │")
    print("│ 向量质量        │ 中等            │ 较高            │")
    print("│ 处理速度        │ 较慢            │ 较快            │")
    print("└─────────────────┴─────────────────┴─────────────────┘")
    
    print("\n🔍 预期改进效果:")
    print("   • 文档数量减少: 预计减少 60-80%")
    print("   • 处理速度提升: 预计提升 2-3 倍")
    print("   • 向量质量提升: 更精准的数据表示")
    print("   • 内存占用减少: 预计减少 40-60%")
    print("   • 分析准确性提升: 更清晰的数据结构")


def main():
    """主函数"""
    print("🚀 CSV风险预测系统测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 比较分析
        compare_with_excel_processing()
        
        # 测试CSV处理
        file_code = test_csv_processing()
        
        # 测试风险预测
        test_risk_prediction(file_code)
        
        print("\n" + "="*60)
        print(" 测试完成 ")
        print("="*60)
        print("✅ CSV风险预测系统测试成功完成!")
        print("\n💡 使用建议:")
        print("   • 对于日常快速筛查，使用智能选择策略")
        print("   • 对于重要决策评估，使用多批次分析策略")
        print("   • CSV格式相比Excel有显著的性能优势")
        print("   • 建议将Excel文件转换为CSV格式以获得更好的性能")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
