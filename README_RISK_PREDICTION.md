# 基于文件编码的风险预测系统

## 系统概述

这是一个基于文件编码的个人信贷风险预测系统，专门用于分析Excel文件中的用户风控特征数据，并通过AI模型预测违约概率。

### 核心特点

- **完全数据隔离**: 每个Excel文件创建独立的向量存储，确保不同用户数据不会相互影响
- **AI驱动分析**: 不预设字段映射，让AI直接分析Excel中的风控特征数据
- **文件编码管理**: 通过唯一的文件编码来标识和管理每个用户的数据
- **灵活数据格式**: 支持任意格式的Excel风控数据，AI自动适应不同的数据结构

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt

# 设置API密钥
export DEEPSEEK_API_KEY="your_deepseek_api_key"
```

### 2. 基本使用

#### 命令行模式

```bash
# 上传Excel文件并获取文件编码
python risk_prediction_main.py --upload data/user_data.xlsx

# 根据文件编码进行风险预测
python risk_prediction_main.py --predict user_data_20250620_123456_abc123

# 列出所有可用文件
python risk_prediction_main.py --list

# 查看系统统计信息
python risk_prediction_main.py --stats
```

#### 交互式模式

```bash
# 启动交互式模式
python risk_prediction_main.py --interactive

# 在交互模式中使用以下命令：
> upload /path/to/excel_file.xlsx    # 上传文件
> predict file_code_here             # 风险预测
> list                               # 列出文件
> stats                              # 统计信息
> help                               # 帮助信息
> exit                               # 退出
```

### 3. 编程接口使用

```python
from src.risk_prediction_api import RiskPredictionAPI

# 创建API实例
api = RiskPredictionAPI()

# 上传Excel文件
result = api.upload_excel_and_get_code("data/user_data.xlsx")
if result["success"]:
    file_code = result["file_code"]
    print(f"文件编码: {file_code}")
    
    # 进行风险预测
    risk_report = api.predict_by_file_code(file_code)
    if risk_report["success"]:
        print(f"违约概率: {risk_report['default_probability']:.2%}")
        print(f"风险等级: {risk_report['risk_level']}")
        print(f"AI分析: {risk_report['analysis_summary']}")
```

## 系统架构

### 核心组件

1. **FileCodedVectorStore** (`src/file_coded_vector_store.py`)
   - 管理基于文件编码的向量存储
   - 确保数据完全隔离

2. **RiskAnalyzer** (`src/risk_analyzer.py`)
   - AI驱动的风险分析器
   - 生成违约概率和风险等级

3. **ExcelRiskProcessor** (`src/excel_risk_processor.py`)
   - Excel文件处理器
   - 将Excel内容转换为知识库文档

4. **RiskPredictionAPI** (`src/risk_prediction_api.py`)
   - 主要API接口
   - 统一的风险预测功能

### 数据流程

```
Excel文件 → 文件编码生成 → 向量存储创建 → AI分析 → 风险报告
```

## Excel数据格式

系统支持任意格式的Excel文件，包含用户的风控特征数据。AI会自动分析以下类型的信息：

- 个人基本信息（年龄、教育、职业等）
- 收入信息（月收入、年收入、收入稳定性等）
- 负债信息（现有负债、负债结构等）
- 信用信息（信用评分、还款历史等）
- 资产信息（房产、车辆、其他资产等）
- 其他风控特征数据

**注意**: 不需要预设字段名称，AI会自动识别和分析Excel中的数据。

## 输出结果

风险预测报告包含：

```json
{
  "success": true,
  "file_code": "user_data_20250620_123456_abc123",
  "default_probability": 0.15,
  "risk_level": "中等风险",
  "risk_score": 0.15,
  "analysis_summary": "AI详细分析结果...",
  "analysis_timestamp": "2025-06-20T10:30:00"
}
```

## 配置说明

主要配置项（`config.py`）：

```python
# DeepSeek API配置
DEEPSEEK_API_KEY = "your_api_key"
DEEPSEEK_MODEL = "deepseek-chat"
TEMPERATURE = 0.1
MAX_TOKENS = 4000

# 风险预测配置
RISK_PREDICTION_ENABLED = True
FILE_CODED_VECTOR_STORE_DIR = "./cache/file_coded_chroma"
MAX_EXCEL_FILE_SIZE_MB = 10
```

## 示例和演示

运行演示程序：

```bash
python examples/risk_prediction_demo.py
```

演示包含：
- 基本使用流程
- 文件管理功能
- 批量风险预测
- 完整工作流程说明

## 文件管理

### 查看所有文件

```python
from src.file_code_manager import FileCodeManager

manager = FileCodeManager()
files = manager.list_available_codes()
for file_info in files:
    print(f"编码: {file_info['file_code']}")
    print(f"文件: {file_info['file_name']}")
    print(f"上传时间: {file_info['created_at']}")
```

### 删除文件

```python
# 删除特定文件编码的数据
result = manager.remove_file_code("file_code_here")
if result["success"]:
    print("文件删除成功")
```

## 注意事项

1. **数据隔离**: 每个Excel文件的数据完全独立，不会相互影响
2. **文件编码**: 文件编码是唯一的，基于文件内容和时间戳生成
3. **API密钥**: 需要有效的DeepSeek API密钥才能进行AI分析
4. **文件大小**: 建议Excel文件不超过10MB
5. **数据安全**: 所有数据存储在本地，不会上传到外部服务器

## 故障排除

### 常见问题

1. **API密钥错误**
   ```
   错误: DeepSeek API 密钥未设置
   解决: export DEEPSEEK_API_KEY="your_api_key"
   ```

2. **文件格式不支持**
   ```
   错误: 不支持的文件格式
   解决: 确保文件是.xlsx或.xls格式
   ```

3. **文件编码不存在**
   ```
   错误: 文件编码不存在
   解决: 使用 --list 查看可用的文件编码
   ```

### 日志查看

系统会输出详细的日志信息，帮助诊断问题：

```bash
# 查看详细日志
python risk_prediction_main.py --interactive 2>&1 | tee risk_prediction.log
```

## 扩展开发

### 自定义分析逻辑

可以通过修改 `src/risk_config.py` 中的prompt模板来自定义分析逻辑：

```python
RISK_ANALYSIS_PROMPT = """
你的自定义分析prompt...
"""
```

### 添加新的数据处理器

可以扩展 `src/excel_risk_processor.py` 来支持更多数据格式。

## 许可证

本项目基于现有的Deep Risk RAG系统扩展开发。
