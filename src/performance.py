"""
性能监控和优化工具
"""

import time
import psutil
import threading
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from contextlib import contextmanager

from .utils import get_logger

logger = get_logger(__name__)


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""

    operation_name: str
    duration: float
    memory_usage_mb: float
    cpu_percent: float
    items_processed: int = 0
    throughput: float = 0.0  # items per second

    def __post_init__(self):
        """计算吞吐量"""
        if self.duration > 0 and self.items_processed > 0:
            self.throughput = self.items_processed / self.duration


class PerformanceMonitor:
    """性能监控器"""

    def __init__(self):
        self.metrics_history: Dict[str, list] = {}
        self._monitoring = False
        self._monitor_thread = None
        self._system_metrics = []

    @contextmanager
    def monitor_operation(self, operation_name: str, items_count: int = 0):
        """
        监控操作性能的上下文管理器

        Args:
            operation_name: 操作名称
            items_count: 处理的项目数量
        """
        # 记录开始状态
        start_time = time.time()
        process = psutil.Process()
        start_memory = process.memory_info().rss / 1024 / 1024  # MB
        start_cpu = process.cpu_percent()

        try:
            yield
        finally:
            # 记录结束状态
            end_time = time.time()
            end_memory = process.memory_info().rss / 1024 / 1024  # MB
            end_cpu = process.cpu_percent()

            # 计算指标
            duration = end_time - start_time
            memory_usage = max(end_memory - start_memory, 0)
            cpu_percent = (start_cpu + end_cpu) / 2

            # 创建性能指标
            metrics = PerformanceMetrics(
                operation_name=operation_name,
                duration=duration,
                memory_usage_mb=memory_usage,
                cpu_percent=cpu_percent,
                items_processed=items_count,
            )

            # 记录指标
            self._record_metrics(metrics)

            # 输出性能日志
            logger.info(
                f"⏱️  {operation_name}: "
                f"耗时 {duration:.2f}s, "
                f"内存 {memory_usage:.1f}MB, "
                f"CPU {cpu_percent:.1f}%, "
                f"吞吐量 {metrics.throughput:.1f} items/s"
            )

    def _record_metrics(self, metrics: PerformanceMetrics):
        """记录性能指标"""
        if metrics.operation_name not in self.metrics_history:
            self.metrics_history[metrics.operation_name] = []

        self.metrics_history[metrics.operation_name].append(metrics)

        # 限制历史记录数量
        if len(self.metrics_history[metrics.operation_name]) > 100:
            self.metrics_history[metrics.operation_name] = self.metrics_history[
                metrics.operation_name
            ][-100:]

    def get_operation_stats(self, operation_name: str) -> Dict[str, Any]:
        """
        获取操作的统计信息

        Args:
            operation_name: 操作名称

        Returns:
            统计信息字典
        """
        if operation_name not in self.metrics_history:
            return {}

        metrics_list = self.metrics_history[operation_name]
        if not metrics_list:
            return {}

        durations = [m.duration for m in metrics_list]
        memory_usages = [m.memory_usage_mb for m in metrics_list]
        throughputs = [m.throughput for m in metrics_list if m.throughput > 0]

        return {
            "operation_name": operation_name,
            "total_runs": len(metrics_list),
            "avg_duration": sum(durations) / len(durations),
            "min_duration": min(durations),
            "max_duration": max(durations),
            "avg_memory_usage": sum(memory_usages) / len(memory_usages),
            "avg_throughput": sum(throughputs) / len(throughputs) if throughputs else 0,
            "last_run": metrics_list[-1],
        }

    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有操作的统计信息"""
        return {
            op_name: self.get_operation_stats(op_name)
            for op_name in self.metrics_history.keys()
        }

    def start_system_monitoring(self, interval: float = 1.0):
        """
        开始系统监控

        Args:
            interval: 监控间隔（秒）
        """
        if self._monitoring:
            return

        self._monitoring = True
        self._monitor_thread = threading.Thread(
            target=self._system_monitor_loop, args=(interval,), daemon=True
        )
        self._monitor_thread.start()
        logger.info("系统监控已启动")

    def stop_system_monitoring(self):
        """停止系统监控"""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=2.0)
        logger.info("系统监控已停止")

    def _system_monitor_loop(self, interval: float):
        """系统监控循环"""
        while self._monitoring:
            try:
                # 获取系统指标
                cpu_percent = psutil.cpu_percent()
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage("/")

                system_metrics = {
                    "timestamp": time.time(),
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_available_gb": memory.available / 1024 / 1024 / 1024,
                    "disk_percent": disk.percent,
                    "disk_free_gb": disk.free / 1024 / 1024 / 1024,
                }

                self._system_metrics.append(system_metrics)

                # 限制历史记录
                if len(self._system_metrics) > 1000:
                    self._system_metrics = self._system_metrics[-1000:]

                time.sleep(interval)

            except Exception as e:
                logger.error(f"系统监控错误: {e}")
                time.sleep(interval)

    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        if not self._system_metrics:
            return {}

        recent_metrics = self._system_metrics[-60:]  # 最近60个数据点

        cpu_values = [m["cpu_percent"] for m in recent_metrics]
        memory_values = [m["memory_percent"] for m in recent_metrics]

        return {
            "current_cpu": recent_metrics[-1]["cpu_percent"],
            "current_memory": recent_metrics[-1]["memory_percent"],
            "current_memory_available_gb": recent_metrics[-1]["memory_available_gb"],
            "current_disk_free_gb": recent_metrics[-1]["disk_free_gb"],
            "avg_cpu_1min": sum(cpu_values) / len(cpu_values),
            "avg_memory_1min": sum(memory_values) / len(memory_values),
            "max_cpu_1min": max(cpu_values),
            "max_memory_1min": max(memory_values),
            "data_points": len(recent_metrics),
        }


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


def monitor_performance(operation_name: str, items_count: int = 0):
    """
    性能监控装饰器

    Args:
        operation_name: 操作名称
        items_count: 处理的项目数量
    """

    def decorator(func: Callable):
        def wrapper(*args, **kwargs):
            with performance_monitor.monitor_operation(operation_name, items_count):
                return func(*args, **kwargs)

        return wrapper

    return decorator


def get_performance_report() -> str:
    """
    生成性能报告

    Returns:
        格式化的性能报告字符串
    """
    all_stats = performance_monitor.get_all_stats()
    system_stats = performance_monitor.get_system_stats()

    report_lines = ["🔍 性能监控报告", "=" * 50]

    # 系统状态
    if system_stats:
        report_lines.extend(
            [
                "\n📊 系统状态:",
                f"  CPU: {system_stats['current_cpu']:.1f}% (1分钟平均: {system_stats['avg_cpu_1min']:.1f}%)",
                f"  内存: {system_stats['current_memory']:.1f}% (可用: {system_stats['current_memory_available_gb']:.1f}GB)",
                f"  磁盘: 剩余 {system_stats['current_disk_free_gb']:.1f}GB",
            ]
        )

    # 操作统计
    if all_stats:
        report_lines.extend(["\n⚡ 操作性能统计:"])
        for op_name, stats in all_stats.items():
            report_lines.extend(
                [
                    f"\n  📋 {op_name}:",
                    f"    运行次数: {stats['total_runs']}",
                    f"    平均耗时: {stats['avg_duration']:.2f}s",
                    f"    平均内存: {stats['avg_memory_usage']:.1f}MB",
                    f"    平均吞吐量: {stats['avg_throughput']:.1f} items/s",
                ]
            )

    return "\n".join(report_lines)


# 导出主要功能
__all__ = [
    "PerformanceMetrics",
    "PerformanceMonitor",
    "performance_monitor",
    "monitor_performance",
    "get_performance_report",
]
