"""
嵌入服务 - 基于 gte-large 模型的文本嵌入服务
"""

import os
from pathlib import Path
from typing import List, Optional, Dict, Any
import time
import hashlib
import pickle

import numpy as np
from sentence_transformers import SentenceTransformer
from langchain_core.embeddings import Embeddings

# 使用统一的工具模块
from .utils import get_logger, load_settings, batch_process, PerformanceTimer

# 获取配置和日志
settings = load_settings()
logger = get_logger(__name__, settings.LOG_LEVEL)


class GTELargeEmbeddings(Embeddings):
    """基于 gte-large 模型的嵌入服务"""

    def __init__(
        self,
        model_name: str = None,
        cache_dir: Optional[str] = None,
        batch_size: int = None,
        max_length: int = None,
        device: str = "auto",
        enable_cache: bool = None,
        cache_ttl: int = None,
        normalize_embeddings: bool = True,
        **kwargs,
    ):
        """
        初始化嵌入服务

        Args:
            model_name: 模型名称
            cache_dir: 模型缓存目录
            batch_size: 批处理大小
            max_length: 最大序列长度
            device: 设备 (cpu, cuda, auto)
            enable_cache: 是否启用嵌入缓存
            cache_ttl: 缓存过期时间（秒）
            normalize_embeddings: 是否标准化嵌入向量
        """
        # 使用配置或默认值
        self.model_name = model_name or settings.EMBEDDING_MODEL_NAME
        self.cache_dir = Path(cache_dir) if cache_dir else settings.MODELS_CACHE_DIR
        self.batch_size = batch_size or settings.EMBEDDING_BATCH_SIZE
        self.max_length = max_length or settings.EMBEDDING_MAX_LENGTH
        self.enable_cache = (
            enable_cache if enable_cache is not None else settings.ENABLE_CACHE
        )
        self.cache_ttl = cache_ttl or settings.CACHE_TTL_SECONDS
        self.normalize_embeddings = normalize_embeddings

        # 设备选择
        if device == "auto":
            import torch

            self.device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            self.device = device

        # 初始化模型
        self.model = None
        self._load_model()

        # 初始化缓存
        self.embedding_cache = {}
        self.cache_dir_path = self.cache_dir / "embedding_cache"
        if self.enable_cache:
            self.cache_dir_path.mkdir(parents=True, exist_ok=True)
            self._load_cache()

    def _load_model(self):
        """加载嵌入模型"""
        try:
            # 设置缓存目录
            os.environ["SENTENCE_TRANSFORMERS_HOME"] = str(self.cache_dir)

            logger.info(f"正在加载嵌入模型: {self.model_name}")
            logger.info(f"设备: {self.device}")
            logger.info(f"缓存目录: {self.cache_dir}")

            # 加载模型
            self.model = SentenceTransformer(
                self.model_name, device=self.device, cache_folder=str(self.cache_dir)
            )

            # 设置最大序列长度
            if hasattr(self.model, "max_seq_length"):
                self.model.max_seq_length = self.max_length

            logger.info(f"✅ 模型加载成功")
            logger.info(f"模型维度: {self.model.get_sentence_embedding_dimension()}")

        except Exception as e:
            logger.error(f"❌ 模型加载失败: {e}")
            raise RuntimeError(f"无法加载嵌入模型: {e}")

    def _get_cache_key(self, text: str) -> str:
        """生成缓存键"""
        # 使用文本内容和模型名称生成唯一键
        content = f"{self.model_name}:{text}"
        return hashlib.md5(content.encode("utf-8")).hexdigest()

    def _load_cache(self):
        """加载缓存"""
        try:
            cache_file = self.cache_dir_path / "embeddings.pkl"
            if cache_file.exists():
                with open(cache_file, "rb") as f:
                    cache_data = pickle.load(f)

                # 检查缓存是否过期
                current_time = time.time()
                valid_cache = {}

                for key, (embedding, timestamp) in cache_data.items():
                    if current_time - timestamp < self.cache_ttl:
                        valid_cache[key] = (embedding, timestamp)

                self.embedding_cache = valid_cache
                logger.info(f"加载嵌入缓存: {len(valid_cache)} 条记录")

        except Exception as e:
            logger.warning(f"加载缓存失败: {e}")
            self.embedding_cache = {}

    def _save_cache(self):
        """保存缓存"""
        if not self.enable_cache:
            return

        try:
            cache_file = self.cache_dir_path / "embeddings.pkl"
            with open(cache_file, "wb") as f:
                pickle.dump(self.embedding_cache, f)
        except Exception as e:
            logger.warning(f"保存缓存失败: {e}")

    def _get_cached_embedding(self, text: str) -> Optional[np.ndarray]:
        """获取缓存的嵌入"""
        if not self.enable_cache:
            return None

        cache_key = self._get_cache_key(text)
        if cache_key in self.embedding_cache:
            embedding, timestamp = self.embedding_cache[cache_key]

            # 检查是否过期
            if time.time() - timestamp < self.cache_ttl:
                return embedding
            else:
                # 删除过期缓存
                del self.embedding_cache[cache_key]

        return None

    def _cache_embedding(self, text: str, embedding: np.ndarray):
        """缓存嵌入"""
        if not self.enable_cache:
            return

        cache_key = self._get_cache_key(text)
        self.embedding_cache[cache_key] = (embedding, time.time())

    def _preprocess_text(self, text: str) -> str:
        """预处理文本"""
        if not text:
            return ""

        # 移除多余的空白字符
        text = " ".join(text.split())

        # 截断过长的文本
        if len(text) > self.max_length * 4:  # 粗略估计，4个字符约等于1个token
            text = text[: self.max_length * 4]
            logger.warning(f"文本过长，已截断到 {len(text)} 字符")

        return text

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        嵌入文档列表

        Args:
            texts: 文本列表

        Returns:
            嵌入向量列表
        """
        if not texts:
            return []

        if self.model is None:
            raise RuntimeError("模型未加载")

        # 预处理文本
        processed_texts = [self._preprocess_text(text) for text in texts]

        # 检查缓存
        embeddings = []
        texts_to_embed = []
        indices_to_embed = []

        for i, text in enumerate(processed_texts):
            cached_embedding = self._get_cached_embedding(text)
            if cached_embedding is not None:
                embeddings.append(cached_embedding)
            else:
                embeddings.append(None)  # 占位符
                texts_to_embed.append(text)
                indices_to_embed.append(i)

        # 优化：批量嵌入未缓存的文本
        if texts_to_embed:
            cache_hit_rate = (len(texts) - len(texts_to_embed)) / len(texts) * 100
            logger.info(
                f"嵌入 {len(texts_to_embed)} 个文本（缓存命中率: {cache_hit_rate:.1f}%）"
            )

            try:
                # 优化：使用 batch_process 工具函数
                from .utils import batch_process

                new_embeddings = []

                for batch_texts in batch_process(texts_to_embed, self.batch_size):
                    # 生成嵌入
                    batch_embeddings = self.model.encode(
                        batch_texts,
                        normalize_embeddings=self.normalize_embeddings,
                        show_progress_bar=len(texts_to_embed) > 10,
                        convert_to_numpy=True,
                    )

                    new_embeddings.extend(batch_embeddings)

                # 更新结果和缓存
                for i, embedding in enumerate(new_embeddings):
                    original_index = indices_to_embed[i]
                    embeddings[original_index] = embedding

                    # 缓存嵌入
                    self._cache_embedding(texts_to_embed[i], embedding)

                # 保存缓存
                if len(texts_to_embed) > 0:
                    self._save_cache()

            except Exception as e:
                logger.error(f"嵌入生成失败: {e}")
                raise

        # 转换为列表格式
        return [embedding.tolist() for embedding in embeddings]

    def embed_query(self, text: str) -> List[float]:
        """
        嵌入查询文本

        Args:
            text: 查询文本

        Returns:
            嵌入向量
        """
        embeddings = self.embed_documents([text])
        return embeddings[0] if embeddings else []

    def similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """
        计算两个嵌入向量的余弦相似度

        Args:
            embedding1: 嵌入向量1
            embedding2: 嵌入向量2

        Returns:
            相似度分数 (0-1)
        """
        import numpy as np

        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)

        # 计算余弦相似度
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        similarity = dot_product / (norm1 * norm2)
        return float(similarity)

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        if self.model is None:
            return {}

        return {
            "model_name": self.model_name,
            "embedding_dimension": self.model.get_sentence_embedding_dimension(),
            "max_sequence_length": getattr(
                self.model, "max_seq_length", self.max_length
            ),
            "device": self.device,
            "cache_enabled": self.enable_cache,
            "cache_size": len(self.embedding_cache),
            "normalize_embeddings": self.normalize_embeddings,
        }

    def clear_cache(self):
        """清空缓存"""
        self.embedding_cache.clear()
        try:
            cache_file = self.cache_dir_path / "embeddings.pkl"
            if cache_file.exists():
                cache_file.unlink()
            logger.info("嵌入缓存已清空")
        except Exception as e:
            logger.warning(f"清空缓存失败: {e}")


# 便捷函数
def create_embeddings(
    model_name: str = None, cache_dir: str = None, device: str = "auto", **kwargs
) -> GTELargeEmbeddings:
    """
    创建嵌入服务实例

    Args:
        model_name: 模型名称
        cache_dir: 缓存目录
        device: 设备
        **kwargs: 其他参数

    Returns:
        嵌入服务实例
    """
    return GTELargeEmbeddings(
        model_name=model_name, cache_dir=cache_dir, device=device, **kwargs
    )
