"""
文档加载器 - 支持多种文档格式的加载和预处理
"""

from pathlib import Path
from typing import List, Optional, Union

from langchain_core.documents import Document
from langchain_community.document_loaders import (
    PyPDFLoader,
    TextLoader,
    UnstructuredWordDocumentLoader,
    UnstructuredExcelLoader,
    UnstructuredPowerPointLoader,
    UnstructuredMarkdownLoader,
)
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain.text_splitter import CharacterTextSplitter

# 使用统一的工具模块
from .utils import (
    get_logger,
    load_settings,
    get_file_size_mb,
    batch_process,
    PerformanceTimer,
)

# 获取配置和日志
settings = load_settings()
logger = get_logger(__name__, settings.LOG_LEVEL)


class DocumentProcessor:
    """文档处理器 - 负责加载、预处理和分块文档"""

    # 支持的文件类型映射
    SUPPORTED_EXTENSIONS = {
        ".pdf": "pdf",
        ".txt": "text",
        ".md": "markdown",
        ".docx": "word",
        ".doc": "word",
        ".xlsx": "excel",
        ".xls": "excel",
        ".pptx": "powerpoint",
        ".ppt": "powerpoint",
    }

    def __init__(
        self,
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        max_file_size_mb: int = 50,
        encoding: str = "utf-8",
    ):
        """
        初始化文档处理器

        Args:
            chunk_size: 文档分块大小
            chunk_overlap: 分块重叠大小
            max_file_size_mb: 最大文件大小限制（MB）
            encoding: 文本文件编码
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.max_file_size_mb = max_file_size_mb
        self.encoding = encoding

        # 初始化文本分割器
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", " ", ""],
        )

        # 备用分割器
        self.char_splitter = CharacterTextSplitter(
            chunk_size=chunk_size, chunk_overlap=chunk_overlap, separator="\n"
        )

    def get_file_type(self, file_path: Union[str, Path]) -> Optional[str]:
        """
        获取文件类型

        Args:
            file_path: 文件路径

        Returns:
            文件类型字符串，如果不支持则返回 None
        """
        file_path = Path(file_path)
        extension = file_path.suffix.lower()
        return self.SUPPORTED_EXTENSIONS.get(extension)

    def validate_file(self, file_path: Union[str, Path]) -> bool:
        """
        验证文件是否可以处理

        Args:
            file_path: 文件路径

        Returns:
            是否可以处理
        """
        file_path = Path(file_path)

        # 检查文件是否存在
        if not file_path.exists():
            logger.error(f"文件不存在: {file_path}")
            return False

        # 检查文件大小
        file_size_mb = file_path.stat().st_size / (1024 * 1024)
        if file_size_mb > self.max_file_size_mb:
            logger.error(f"文件过大: {file_size_mb:.1f}MB > {self.max_file_size_mb}MB")
            return False

        # 检查文件类型
        file_type = self.get_file_type(file_path)
        if file_type is None:
            logger.error(f"不支持的文件类型: {file_path.suffix}")
            return False

        return True

    def load_pdf(self, file_path: Union[str, Path]) -> List[Document]:
        """加载 PDF 文档"""
        try:
            loader = PyPDFLoader(str(file_path))
            documents = loader.load()
            logger.info(f"成功加载 PDF: {file_path}, 页数: {len(documents)}")
            return documents
        except Exception as e:
            logger.error(f"加载 PDF 失败 {file_path}: {e}")
            return []

    def load_text(self, file_path: Union[str, Path]) -> List[Document]:
        """加载文本文档"""
        try:
            loader = TextLoader(str(file_path), encoding=self.encoding)
            documents = loader.load()
            logger.info(f"成功加载文本文件: {file_path}")
            return documents
        except Exception as e:
            logger.error(f"加载文本文件失败 {file_path}: {e}")
            return []

    def load_markdown(self, file_path: Union[str, Path]) -> List[Document]:
        """加载 Markdown 文档"""
        try:
            loader = UnstructuredMarkdownLoader(str(file_path))
            documents = loader.load()
            logger.info(f"成功加载 Markdown: {file_path}")
            return documents
        except Exception as e:
            logger.error(f"加载 Markdown 失败 {file_path}: {e}")
            return []

    def load_word(self, file_path: Union[str, Path]) -> List[Document]:
        """加载 Word 文档"""
        try:
            loader = UnstructuredWordDocumentLoader(str(file_path))
            documents = loader.load()
            logger.info(f"成功加载 Word 文档: {file_path}")
            return documents
        except Exception as e:
            logger.error(f"加载 Word 文档失败 {file_path}: {e}")
            return []

    def load_excel(self, file_path: Union[str, Path]) -> List[Document]:
        """加载 Excel 文档"""
        try:
            loader = UnstructuredExcelLoader(str(file_path))
            documents = loader.load()
            logger.info(f"成功加载 Excel: {file_path}")
            return documents
        except Exception as e:
            logger.error(f"加载 Excel 失败 {file_path}: {e}")
            return []

    def load_powerpoint(self, file_path: Union[str, Path]) -> List[Document]:
        """加载 PowerPoint 文档"""
        try:
            loader = UnstructuredPowerPointLoader(str(file_path))
            documents = loader.load()
            logger.info(f"成功加载 PowerPoint: {file_path}")
            return documents
        except Exception as e:
            logger.error(f"加载 PowerPoint 失败 {file_path}: {e}")
            return []

    def load_single_file(self, file_path: Union[str, Path]) -> List[Document]:
        """
        加载单个文件

        Args:
            file_path: 文件路径

        Returns:
            文档列表
        """
        file_path = Path(file_path)

        # 验证文件
        if not self.validate_file(file_path):
            return []

        # 获取文件类型
        file_type = self.get_file_type(file_path)

        # 根据文件类型选择加载器
        loader_map = {
            "pdf": self.load_pdf,
            "text": self.load_text,
            "markdown": self.load_markdown,
            "word": self.load_word,
            "excel": self.load_excel,
            "powerpoint": self.load_powerpoint,
        }

        loader_func = loader_map.get(file_type)
        if loader_func is None:
            logger.error(f"未找到对应的加载器: {file_type}")
            return []

        # 加载文档
        documents = loader_func(file_path)

        # 优化：预计算文件信息，避免重复调用
        if documents:
            file_info = {
                "source": str(file_path),
                "file_type": file_type,
                "file_name": file_path.name,
                "file_size": file_path.stat().st_size,
            }

            # 批量更新元数据
            for doc in documents:
                doc.metadata.update(file_info)

        return documents

    def preprocess_text(self, text: str) -> str:
        """
        预处理文本

        Args:
            text: 原始文本

        Returns:
            处理后的文本
        """
        if not text:
            return ""

        # 移除多余的空白字符
        text = " ".join(text.split())

        # 移除特殊字符（保留中英文、数字、常用标点）
        import re

        text = re.sub(r'[^\w\s\u4e00-\u9fff.,!?;:()[\]{}"\'-]', " ", text)

        # 再次清理空白字符
        text = " ".join(text.split())

        return text

    def split_documents(self, documents: List[Document]) -> List[Document]:
        """
        分割文档

        Args:
            documents: 原始文档列表

        Returns:
            分割后的文档列表
        """
        if not documents:
            return []

        split_docs = []

        for doc in documents:
            # 预处理文本
            processed_text = self.preprocess_text(doc.page_content)

            if not processed_text.strip():
                logger.warning(
                    f"文档内容为空，跳过: {doc.metadata.get('source', 'unknown')}"
                )
                continue

            # 创建新的文档对象
            processed_doc = Document(
                page_content=processed_text, metadata=doc.metadata.copy()
            )

            try:
                # 尝试使用递归分割器
                chunks = self.text_splitter.split_documents([processed_doc])
            except Exception as e:
                logger.warning(f"递归分割失败，使用字符分割器: {e}")
                try:
                    chunks = self.char_splitter.split_documents([processed_doc])
                except Exception as e2:
                    logger.error(f"文档分割失败: {e2}")
                    continue

            # 优化：批量添加元数据，避免重复计算
            total_chunks = len(chunks)
            for i, chunk in enumerate(chunks):
                chunk.metadata.update(
                    {
                        "chunk_id": i,
                        "chunk_size": len(chunk.page_content),
                        "total_chunks": total_chunks,
                    }
                )

            split_docs.extend(chunks)

        logger.info(f"文档分割完成: {len(documents)} -> {len(split_docs)} 个分块")
        return split_docs

    def load_directory(self, directory_path: Union[str, Path]) -> List[Document]:
        """
        加载目录中的所有支持文档

        Args:
            directory_path: 目录路径

        Returns:
            所有文档的列表
        """
        directory_path = Path(directory_path)

        if not directory_path.exists() or not directory_path.is_dir():
            logger.error(f"目录不存在或不是目录: {directory_path}")
            return []

        all_documents = []
        processed_files = 0
        skipped_files = 0

        # 遍历目录中的所有文件
        for file_path in directory_path.rglob("*"):
            if file_path.is_file():
                file_type = self.get_file_type(file_path)
                if file_type:
                    documents = self.load_single_file(file_path)
                    if documents:
                        all_documents.extend(documents)
                        processed_files += 1
                    else:
                        skipped_files += 1
                else:
                    skipped_files += 1

        logger.info(
            f"目录处理完成: 处理 {processed_files} 个文件，跳过 {skipped_files} 个文件"
        )
        return all_documents

    def process_documents(
        self,
        file_paths: Union[str, Path, List[Union[str, Path]]],
        split_documents: bool = True,
    ) -> List[Document]:
        """
        处理文档（加载 + 分割）

        Args:
            file_paths: 文件路径或路径列表，也可以是目录路径
            split_documents: 是否分割文档

        Returns:
            处理后的文档列表
        """
        if isinstance(file_paths, (str, Path)):
            file_paths = [file_paths]

        all_documents = []

        for path in file_paths:
            path = Path(path)

            if path.is_dir():
                # 处理目录
                documents = self.load_directory(path)
            else:
                # 处理单个文件
                documents = self.load_single_file(path)

            all_documents.extend(documents)

        if split_documents and all_documents:
            all_documents = self.split_documents(all_documents)

        logger.info(f"文档处理完成: 总共 {len(all_documents)} 个文档块")
        return all_documents


# 便捷函数
def load_documents(
    file_paths: Union[str, Path, List[Union[str, Path]]],
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
    split_documents: bool = True,
) -> List[Document]:
    """
    便捷的文档加载函数

    Args:
        file_paths: 文件路径或路径列表
        chunk_size: 分块大小
        chunk_overlap: 分块重叠
        split_documents: 是否分割文档

    Returns:
        文档列表
    """
    processor = DocumentProcessor(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
    return processor.process_documents(file_paths, split_documents)
