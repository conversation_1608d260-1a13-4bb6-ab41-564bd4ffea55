"""
CSV风控数据处理器
专门处理CSV格式的风控数据文件，优化文档分块和向量生成
"""

import logging
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
import hashlib
import uuid

from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter

from .risk_config import RiskAnalysisConfig

logger = logging.getLogger(__name__)


class CSVRiskProcessor:
    """
    CSV风控数据处理器

    专门处理CSV格式的风控数据，相比Excel处理器有以下优势：
    1. 更少的文档数量（无工作表概念）
    2. 更高的处理效率（纯文本格式）
    3. 更精准的数据分块（基于行数）
    4. 更简洁的数据结构
    """

    def __init__(
        self,
        encoding: str = "utf-8",
        rows_per_chunk: int = 100,  # 每个文档块包含的行数
        chunk_overlap_rows: int = 10,  # 分块重叠的行数
    ):
        """
        初始化CSV处理器

        Args:
            encoding: 文件编码
            rows_per_chunk: 每个文档块包含的行数
            chunk_overlap_rows: 分块重叠的行数
        """
        self.encoding = encoding
        self.rows_per_chunk = rows_per_chunk
        self.chunk_overlap_rows = chunk_overlap_rows
        self.risk_config = RiskAnalysisConfig()

        logger.info(f"CSV风控数据处理器初始化完成")
        logger.info(f"每块行数: {rows_per_chunk}, 重叠行数: {chunk_overlap_rows}")

    def generate_file_code(self, file_path: Path) -> str:
        """
        生成文件编码

        Args:
            file_path: 文件路径

        Returns:
            唯一的文件编码
        """
        # 使用文件内容的哈希值 + 时间戳 + 随机UUID
        with open(file_path, "rb") as f:
            file_hash = hashlib.md5(f.read()).hexdigest()

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        random_suffix = str(uuid.uuid4()).replace("-", "")[:12]

        return f"{file_hash}_{timestamp}_{random_suffix}"

    def create_documents_from_csv(
        self, file_path: Path, file_code: Optional[str] = None
    ) -> List[Document]:
        """
        从CSV文件创建文档列表

        Args:
            file_path: CSV文件路径
            file_code: 文件编码（可选）

        Returns:
            文档列表
        """
        try:
            if file_code is None:
                file_code = self.generate_file_code(file_path)

            logger.info(f"开始处理CSV文件: {file_path}")
            logger.info(f"文件编码: {file_code}")

            # 读取CSV文件
            df = pd.read_csv(file_path, encoding=self.encoding)
            logger.info(f"CSV文件读取成功，共 {len(df)} 行，{len(df.columns)} 列")

            # 生成文档
            documents = self._create_chunked_documents(df, file_path, file_code)

            logger.info(f"✅ CSV文件处理完成，生成 {len(documents)} 个文档")
            return documents

        except Exception as e:
            logger.error(f"处理CSV文件失败: {e}")
            raise

    def _create_chunked_documents(
        self, df: pd.DataFrame, file_path: Path, file_code: str
    ) -> List[Document]:
        """
        创建分块文档

        Args:
            df: DataFrame数据
            file_path: 文件路径
            file_code: 文件编码

        Returns:
            分块文档列表
        """
        documents = []
        total_rows = len(df)

        # 计算需要的块数
        num_chunks = (total_rows + self.rows_per_chunk - 1) // self.rows_per_chunk
        logger.info(f"将 {total_rows} 行数据分为 {num_chunks} 个文档块")

        for chunk_id in range(num_chunks):
            # 计算当前块的行范围
            start_row = chunk_id * self.rows_per_chunk
            end_row = min(start_row + self.rows_per_chunk, total_rows)

            # 添加重叠行（除了第一块）
            if chunk_id > 0:
                overlap_start = max(0, start_row - self.chunk_overlap_rows)
                chunk_df = df.iloc[overlap_start:end_row]
                actual_start = overlap_start
            else:
                chunk_df = df.iloc[start_row:end_row]
                actual_start = start_row

            # 生成块内容
            chunk_content = self._format_chunk_content(
                chunk_df, chunk_id, actual_start, end_row - 1, total_rows
            )

            # 创建文档
            doc = Document(
                page_content=chunk_content,
                metadata={
                    "source": str(file_path),
                    "file_name": file_path.name,
                    "file_code": file_code,
                    "chunk_id": chunk_id,
                    "chunk_start_row": actual_start,
                    "chunk_end_row": end_row - 1,
                    "total_chunks": num_chunks,
                    "total_rows": total_rows,
                    "data_type": "csv_risk_data",
                    "created_at": datetime.now().isoformat(),
                    "chunk_size": len(chunk_content),
                    "rows_in_chunk": len(chunk_df),
                },
            )

            documents.append(doc)
            logger.debug(
                f"创建文档块 {chunk_id + 1}/{num_chunks}，行范围: {actual_start}-{end_row-1}"
            )

        return documents

    def _format_chunk_content(
        self,
        chunk_df: pd.DataFrame,
        chunk_id: int,
        start_row: int,
        end_row: int,
        total_rows: int,
    ) -> str:
        """
        格式化块内容

        Args:
            chunk_df: 块数据
            chunk_id: 块ID
            start_row: 起始行
            end_row: 结束行
            total_rows: 总行数

        Returns:
            格式化的内容字符串
        """
        content_parts = [
            f"=== 风控数据块 {chunk_id + 1} ===",
            f"数据范围: 第 {start_row + 1} 行 - 第 {end_row + 1} 行 (共 {total_rows} 行)",
            f"本块行数: {len(chunk_df)}",
            f"数据列: {', '.join(chunk_df.columns.tolist())}",
            "",
            "=== 数据内容 ===",
        ]

        # 添加表头
        content_parts.append("列名: " + " | ".join(chunk_df.columns.tolist()))
        content_parts.append("-" * 50)

        # 添加数据行
        for idx, (_, row) in enumerate(chunk_df.iterrows()):
            row_data = []
            for col in chunk_df.columns:
                value = row[col]
                # 处理空值
                if pd.isna(value):
                    value = "空值"
                else:
                    value = str(value)
                row_data.append(value)

            content_parts.append(f"第{start_row + idx + 1}行: " + " | ".join(row_data))

        # 添加统计信息
        content_parts.extend(["", "=== 数据统计 ===", f"数值列统计:"])

        # 添加数值列的基本统计
        numeric_cols = chunk_df.select_dtypes(include=["number"]).columns
        for col in numeric_cols:
            if not chunk_df[col].empty:
                stats = chunk_df[col].describe()
                content_parts.append(
                    f"{col}: 均值={stats['mean']:.2f}, 最大值={stats['max']}, 最小值={stats['min']}"
                )

        return "\n".join(content_parts)

    def analyze_csv_structure(self, file_path: Path) -> Dict[str, Any]:
        """
        分析CSV文件结构

        Args:
            file_path: CSV文件路径

        Returns:
            文件结构分析结果
        """
        try:
            df = pd.read_csv(file_path, encoding=self.encoding)

            analysis = {
                "file_name": file_path.name,
                "total_rows": len(df),
                "total_columns": len(df.columns),
                "columns": df.columns.tolist(),
                "data_types": df.dtypes.to_dict(),
                "missing_values": df.isnull().sum().to_dict(),
                "numeric_columns": df.select_dtypes(
                    include=["number"]
                ).columns.tolist(),
                "text_columns": df.select_dtypes(include=["object"]).columns.tolist(),
                "estimated_chunks": (len(df) + self.rows_per_chunk - 1)
                // self.rows_per_chunk,
                "file_size_mb": file_path.stat().st_size / (1024 * 1024),
            }

            # 添加数值列统计
            if analysis["numeric_columns"]:
                analysis["numeric_stats"] = (
                    df[analysis["numeric_columns"]].describe().to_dict()
                )

            logger.info(
                f"CSV文件分析完成: {analysis['total_rows']}行 x {analysis['total_columns']}列"
            )
            return analysis

        except Exception as e:
            logger.error(f"分析CSV文件失败: {e}")
            raise

    def validate_csv_file(self, file_path: Path) -> Dict[str, Any]:
        """
        验证CSV文件是否适合风控分析

        Args:
            file_path: CSV文件路径

        Returns:
            验证结果
        """
        try:
            analysis = self.analyze_csv_structure(file_path)

            validation = {
                "is_valid": True,
                "warnings": [],
                "recommendations": [],
                "analysis": analysis,
            }

            # 检查文件大小
            if analysis["file_size_mb"] > 100:
                validation["warnings"].append(
                    f"文件较大 ({analysis['file_size_mb']:.1f}MB)，处理可能较慢"
                )

            # 检查行数
            if analysis["total_rows"] < 10:
                validation["warnings"].append("数据行数较少，可能影响分析质量")
            elif analysis["total_rows"] > 10000:
                validation["recommendations"].append(
                    "数据量较大，建议使用多批次分析策略"
                )

            # 检查列数
            if analysis["total_columns"] < 5:
                validation["warnings"].append("数据列数较少，可能缺少关键风控指标")

            # 检查缺失值
            high_missing_cols = [
                col
                for col, missing in analysis["missing_values"].items()
                if missing > analysis["total_rows"] * 0.5
            ]
            if high_missing_cols:
                validation["warnings"].append(
                    f"以下列缺失值较多: {', '.join(high_missing_cols)}"
                )

            # 检查数值列
            if len(analysis["numeric_columns"]) < 3:
                validation["warnings"].append("数值列较少，可能影响风险评估的准确性")

            return validation

        except Exception as e:
            return {
                "is_valid": False,
                "error": str(e),
                "warnings": [],
                "recommendations": [],
            }
