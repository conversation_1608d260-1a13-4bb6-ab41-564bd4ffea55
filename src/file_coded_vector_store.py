"""
基于文件编码的向量存储管理器
"""

import os
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import logging
from datetime import datetime

from langchain_core.documents import Document
from langchain_core.vectorstores import VectorStore

# 导入现有的向量存储
from .vector_store import ChromaVectorStore
from .embeddings import GTELargeEmbeddings

# 获取日志
logger = logging.getLogger(__name__)


class FileCodedVectorStore:
    """基于文件编码的向量存储管理器"""
    
    def __init__(
        self,
        base_persist_dir: str = "./cache/file_coded_chroma",
        embeddings: Optional[GTELargeEmbeddings] = None
    ):
        """
        初始化文件编码向量存储管理器
        
        Args:
            base_persist_dir: 基础持久化目录
            embeddings: 嵌入模型实例
        """
        self.base_persist_dir = Path(base_persist_dir)
        self.base_persist_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化嵌入模型
        if embeddings is None:
            self.embeddings = GTELargeEmbeddings()
        else:
            self.embeddings = embeddings
            
        # 存储已创建的向量存储实例
        self._vector_stores: Dict[str, ChromaVectorStore] = {}
        
        # 文件编码映射表
        self._file_code_mapping: Dict[str, Dict[str, Any]] = {}
        
        logger.info(f"文件编码向量存储管理器初始化完成，基础目录: {self.base_persist_dir}")
    
    def generate_file_code(self, file_path: Union[str, Path], content_hash: str = None) -> str:
        """
        生成文件编码
        
        Args:
            file_path: 文件路径
            content_hash: 文件内容哈希（可选）
            
        Returns:
            文件编码
        """
        file_path = Path(file_path)
        
        # 基于文件路径和时间戳生成编码
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = file_path.stem
        
        # 如果提供了内容哈希，使用内容哈希
        if content_hash:
            hash_part = content_hash[:8]
        else:
            # 基于文件路径和时间戳生成哈希
            hash_input = f"{file_path}_{timestamp}".encode('utf-8')
            hash_part = hashlib.md5(hash_input).hexdigest()[:8]
        
        file_code = f"{file_name}_{timestamp}_{hash_part}"
        
        logger.info(f"生成文件编码: {file_code} for {file_path}")
        return file_code
    
    def get_collection_name_by_code(self, file_code: str) -> str:
        """根据文件编码获取集合名称"""
        return f"risk_analysis_{file_code}"
    
    def get_persist_dir_by_code(self, file_code: str) -> str:
        """根据文件编码获取持久化目录"""
        return str(self.base_persist_dir / file_code)
    
    def create_vector_store_for_file(
        self, 
        file_code: str, 
        documents: List[Document],
        file_info: Dict[str, Any] = None
    ) -> ChromaVectorStore:
        """
        为特定文件创建向量存储
        
        Args:
            file_code: 文件编码
            documents: 文档列表
            file_info: 文件信息
            
        Returns:
            向量存储实例
        """
        if not documents:
            raise ValueError("文档列表不能为空")
        
        # 获取集合名称和持久化目录
        collection_name = self.get_collection_name_by_code(file_code)
        persist_dir = self.get_persist_dir_by_code(file_code)
        
        # 创建向量存储实例
        vector_store = ChromaVectorStore(
            collection_name=collection_name,
            persist_directory=persist_dir,
            embeddings=self.embeddings
        )
        
        # 添加文档到向量存储
        vector_store.add_documents(documents)
        
        # 缓存向量存储实例
        self._vector_stores[file_code] = vector_store
        
        # 记录文件编码映射
        self._file_code_mapping[file_code] = {
            "collection_name": collection_name,
            "persist_dir": persist_dir,
            "document_count": len(documents),
            "created_at": datetime.now().isoformat(),
            "file_info": file_info or {}
        }
        
        logger.info(f"为文件编码 {file_code} 创建向量存储，文档数量: {len(documents)}")
        return vector_store
    
    def get_vector_store_by_code(self, file_code: str) -> Optional[ChromaVectorStore]:
        """
        根据文件编码获取向量存储
        
        Args:
            file_code: 文件编码
            
        Returns:
            向量存储实例，如果不存在则返回None
        """
        # 先检查缓存
        if file_code in self._vector_stores:
            return self._vector_stores[file_code]
        
        # 检查持久化目录是否存在
        persist_dir = self.get_persist_dir_by_code(file_code)
        if not Path(persist_dir).exists():
            logger.warning(f"文件编码 {file_code} 对应的向量存储不存在")
            return None
        
        # 重新加载向量存储
        collection_name = self.get_collection_name_by_code(file_code)
        vector_store = ChromaVectorStore(
            collection_name=collection_name,
            persist_directory=persist_dir,
            embeddings=self.embeddings
        )
        
        # 缓存实例
        self._vector_stores[file_code] = vector_store
        
        logger.info(f"重新加载文件编码 {file_code} 的向量存储")
        return vector_store
    
    def search_by_file_code(
        self, 
        file_code: str, 
        query: str, 
        k: int = 5
    ) -> List[Document]:
        """
        在特定文件编码的向量存储中搜索
        
        Args:
            file_code: 文件编码
            query: 查询文本
            k: 返回结果数量
            
        Returns:
            相关文档列表
        """
        vector_store = self.get_vector_store_by_code(file_code)
        if vector_store is None:
            logger.error(f"文件编码 {file_code} 对应的向量存储不存在")
            return []
        
        try:
            results = vector_store.similarity_search(query, k=k)
            logger.info(f"在文件编码 {file_code} 中搜索到 {len(results)} 个相关文档")
            return results
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return []
    
    def get_all_documents_by_code(self, file_code: str) -> List[Document]:
        """
        获取特定文件编码的所有文档
        
        Args:
            file_code: 文件编码
            
        Returns:
            所有文档列表
        """
        vector_store = self.get_vector_store_by_code(file_code)
        if vector_store is None:
            return []
        
        try:
            # 使用一个通用查询获取所有文档
            all_docs = vector_store.similarity_search("", k=1000)  # 假设不超过1000个文档
            logger.info(f"获取文件编码 {file_code} 的所有文档，数量: {len(all_docs)}")
            return all_docs
        except Exception as e:
            logger.error(f"获取所有文档失败: {e}")
            return []
    
    def list_file_codes(self) -> List[str]:
        """
        列出所有可用的文件编码
        
        Returns:
            文件编码列表
        """
        file_codes = []
        
        # 从持久化目录中扫描
        if self.base_persist_dir.exists():
            for item in self.base_persist_dir.iterdir():
                if item.is_dir():
                    file_codes.append(item.name)
        
        # 合并缓存中的编码
        file_codes.extend(self._vector_stores.keys())
        
        # 去重并排序
        file_codes = sorted(list(set(file_codes)))
        
        logger.info(f"找到 {len(file_codes)} 个文件编码")
        return file_codes
    
    def get_file_info_by_code(self, file_code: str) -> Optional[Dict[str, Any]]:
        """
        获取文件编码对应的信息
        
        Args:
            file_code: 文件编码
            
        Returns:
            文件信息字典
        """
        return self._file_code_mapping.get(file_code)
    
    def remove_file_code(self, file_code: str) -> bool:
        """
        删除文件编码及其对应的向量存储
        
        Args:
            file_code: 文件编码
            
        Returns:
            是否删除成功
        """
        try:
            # 从缓存中移除
            if file_code in self._vector_stores:
                del self._vector_stores[file_code]
            
            # 从映射表中移除
            if file_code in self._file_code_mapping:
                del self._file_code_mapping[file_code]
            
            # 删除持久化目录
            persist_dir = Path(self.get_persist_dir_by_code(file_code))
            if persist_dir.exists():
                import shutil
                shutil.rmtree(persist_dir)
            
            logger.info(f"成功删除文件编码 {file_code}")
            return True
            
        except Exception as e:
            logger.error(f"删除文件编码 {file_code} 失败: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息字典
        """
        file_codes = self.list_file_codes()
        total_documents = 0
        
        for file_code in file_codes:
            info = self.get_file_info_by_code(file_code)
            if info:
                total_documents += info.get("document_count", 0)
        
        return {
            "total_file_codes": len(file_codes),
            "total_documents": total_documents,
            "active_vector_stores": len(self._vector_stores),
            "base_persist_dir": str(self.base_persist_dir)
        }
