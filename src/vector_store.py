"""
向量数据库 - 基于 Chroma 的向量存储和检索服务
"""

from pathlib import Path
from typing import List, Dict, Any, Optional, Union, Tuple
import uuid
from datetime import datetime

from langchain_core.documents import Document
from langchain_chroma import Chroma
from langchain_core.embeddings import Embeddings

# 使用统一的工具模块
from .utils import get_logger, load_settings

# 获取配置和日志
settings = load_settings()
logger = get_logger(__name__, settings.LOG_LEVEL)


class ChromaVectorStore:
    """基于 Chroma 的向量存储服务"""

    def __init__(
        self,
        embeddings: Optional[Embeddings] = None,
        persist_directory: Optional[str] = None,
        collection_name: Optional[str] = None,
        **kwargs,
    ):
        """
        初始化向量存储

        Args:
            embeddings: 嵌入服务实例
            persist_directory: 持久化目录
            collection_name: 集合名称
        """
        # 初始化嵌入服务
        if embeddings is None:
            logger.info("未提供嵌入服务，使用默认嵌入服务")
            # 延迟导入避免循环依赖
            from .embeddings import GTELargeEmbeddings

            self.embeddings = GTELargeEmbeddings()
        else:
            self.embeddings = embeddings

        # 配置参数
        self.persist_directory = persist_directory or settings.CHROMA_PERSIST_DIR
        self.collection_name = collection_name or settings.CHROMA_COLLECTION_NAME

        # 确保持久化目录存在
        Path(self.persist_directory).mkdir(parents=True, exist_ok=True)

        # 初始化 Chroma 向量存储
        self.vector_store = None
        self._initialize_vector_store()

        logger.info(f"✅ 向量存储初始化完成")
        logger.info(f"持久化目录: {self.persist_directory}")
        logger.info(f"集合名称: {self.collection_name}")

    def _initialize_vector_store(self):
        """初始化 Chroma 向量存储"""
        try:
            self.vector_store = Chroma(
                collection_name=self.collection_name,
                embedding_function=self.embeddings,
                persist_directory=self.persist_directory,
            )
            logger.info("Chroma 向量存储初始化成功")

        except Exception as e:
            logger.error(f"Chroma 向量存储初始化失败: {e}")
            raise RuntimeError(f"无法初始化向量存储: {e}")

    def add_documents(
        self, documents: List[Document], ids: Optional[List[str]] = None, **kwargs
    ) -> List[str]:
        """
        添加文档到向量存储

        Args:
            documents: 文档列表
            ids: 文档ID列表，如果为None则自动生成

        Returns:
            文档ID列表
        """
        if not documents:
            logger.warning("没有文档需要添加")
            return []

        try:
            # 生成文档ID（如果未提供）
            if ids is None:
                ids = [str(uuid.uuid4()) for _ in documents]

            # 优化：为文档添加时间戳，避免低效的 index() 调用
            current_time = datetime.now().isoformat()
            for i, doc in enumerate(documents):
                doc.metadata.update({"added_at": current_time, "doc_id": ids[i]})

            # 添加到向量存储
            logger.info(f"正在添加 {len(documents)} 个文档到向量存储...")

            result_ids = self.vector_store.add_documents(
                documents=documents, ids=ids, **kwargs
            )

            logger.info(f"✅ 成功添加 {len(result_ids)} 个文档")
            return result_ids

        except Exception as e:
            logger.error(f"添加文档失败: {e}")
            raise

    def similarity_search(
        self,
        query: str,
        k: int = None,
        filter: Optional[Dict[str, Any]] = None,
        **kwargs,
    ) -> List[Document]:
        """
        相似度搜索

        Args:
            query: 查询文本
            k: 返回结果数量
            filter: 过滤条件

        Returns:
            相似文档列表
        """
        if k is None:
            k = settings.RETRIEVAL_TOP_K

        try:
            logger.info(f"执行相似度搜索: '{query[:50]}...' (k={k})")

            results = self.vector_store.similarity_search(
                query=query, k=k, filter=filter, **kwargs
            )

            logger.info(f"找到 {len(results)} 个相似文档")
            return results

        except Exception as e:
            logger.error(f"相似度搜索失败: {e}")
            return []

    def similarity_search_with_score(
        self,
        query: str,
        k: int = None,
        filter: Optional[Dict[str, Any]] = None,
        score_threshold: float = None,
        **kwargs,
    ) -> List[Tuple[Document, float]]:
        """
        带分数的相似度搜索

        Args:
            query: 查询文本
            k: 返回结果数量
            filter: 过滤条件
            score_threshold: 分数阈值

        Returns:
            (文档, 分数) 元组列表
        """
        if k is None:
            k = settings.RETRIEVAL_TOP_K
        if score_threshold is None:
            score_threshold = settings.SIMILARITY_THRESHOLD

        try:
            logger.info(
                f"执行带分数的相似度搜索: '{query[:50]}...' (k={k}, threshold={score_threshold})"
            )

            results = self.vector_store.similarity_search_with_score(
                query=query, k=k, filter=filter, **kwargs
            )

            # 过滤低分数结果
            filtered_results = [
                (doc, score) for doc, score in results if score >= score_threshold
            ]

            logger.info(
                f"找到 {len(results)} 个结果，过滤后 {len(filtered_results)} 个"
            )
            return filtered_results

        except Exception as e:
            logger.error(f"带分数的相似度搜索失败: {e}")
            return []

    def delete_documents(self, ids: List[str]) -> bool:
        """
        删除文档

        Args:
            ids: 要删除的文档ID列表

        Returns:
            是否删除成功
        """
        try:
            logger.info(f"正在删除 {len(ids)} 个文档")
            self.vector_store.delete(ids=ids)
            logger.info("✅ 文档删除成功")
            return True

        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            return False

    def get_collection_info(self) -> Dict[str, Any]:
        """
        获取集合信息

        Returns:
            集合信息字典
        """
        try:
            # 获取集合统计信息
            collection = self.vector_store._collection
            count = collection.count()

            return {
                "collection_name": self.collection_name,
                "document_count": count,
                "persist_directory": self.persist_directory,
                "embedding_function": type(self.embeddings).__name__,
            }

        except Exception as e:
            logger.error(f"获取集合信息失败: {e}")
            return {}

    def clear_collection(self) -> bool:
        """
        清空集合

        Returns:
            是否清空成功
        """
        try:
            logger.warning("正在清空向量存储集合...")

            # 获取所有文档ID
            collection = self.vector_store._collection
            all_ids = collection.get()["ids"]

            if all_ids:
                self.vector_store.delete(ids=all_ids)
                logger.info(f"✅ 已清空 {len(all_ids)} 个文档")
            else:
                logger.info("集合已经是空的")

            return True

        except Exception as e:
            logger.error(f"清空集合失败: {e}")
            return False

    def as_retriever(self, **kwargs):
        """
        转换为检索器

        Returns:
            LangChain 检索器
        """
        return self.vector_store.as_retriever(**kwargs)


class VectorStoreManager:
    """向量存储管理器"""

    def __init__(self, vector_store: ChromaVectorStore):
        """
        初始化管理器

        Args:
            vector_store: 向量存储实例
        """
        self.vector_store = vector_store

    def index_documents_from_directory(
        self,
        directory_path: Union[str, Path],
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        clear_existing: bool = False,
    ) -> Dict[str, Any]:
        """
        从目录索引文档

        Args:
            directory_path: 文档目录路径
            chunk_size: 分块大小
            chunk_overlap: 分块重叠
            clear_existing: 是否清空现有数据

        Returns:
            索引结果统计
        """
        from src.document_loader import DocumentProcessor

        try:
            # 清空现有数据（如果需要）
            if clear_existing:
                self.vector_store.clear_collection()

            # 加载和处理文档
            processor = DocumentProcessor(
                chunk_size=chunk_size, chunk_overlap=chunk_overlap
            )

            documents = processor.load_directory(directory_path)
            if not documents:
                logger.warning(f"目录中没有找到可处理的文档: {directory_path}")
                return {"success": False, "message": "没有找到文档"}

            # 分割文档
            split_documents = processor.split_documents(documents)

            # 添加到向量存储
            doc_ids = self.vector_store.add_documents(split_documents)

            result = {
                "success": True,
                "original_documents": len(documents),
                "split_documents": len(split_documents),
                "indexed_documents": len(doc_ids),
                "directory": str(directory_path),
            }

            logger.info(f"✅ 文档索引完成: {result}")
            return result

        except Exception as e:
            logger.error(f"文档索引失败: {e}")
            return {"success": False, "error": str(e)}

    def search_documents(
        self,
        query: str,
        top_k: int = 5,
        include_scores: bool = True,
        score_threshold: float = None,
    ) -> Dict[str, Any]:
        """
        搜索文档

        Args:
            query: 查询文本
            top_k: 返回结果数量
            include_scores: 是否包含分数
            score_threshold: 分数阈值

        Returns:
            搜索结果
        """
        try:
            if include_scores:
                results = self.vector_store.similarity_search_with_score(
                    query=query, k=top_k, score_threshold=score_threshold
                )

                formatted_results = []
                for doc, score in results:
                    formatted_results.append(
                        {
                            "content": doc.page_content,
                            "metadata": doc.metadata,
                            "score": float(score),
                        }
                    )

            else:
                results = self.vector_store.similarity_search(query=query, k=top_k)

                formatted_results = []
                for doc in results:
                    formatted_results.append(
                        {"content": doc.page_content, "metadata": doc.metadata}
                    )

            return {
                "success": True,
                "query": query,
                "results": formatted_results,
                "total_results": len(formatted_results),
            }

        except Exception as e:
            logger.error(f"文档搜索失败: {e}")
            return {"success": False, "error": str(e)}


# 便捷函数
def create_vector_store(
    embeddings: Optional[Embeddings] = None,
    persist_directory: Optional[str] = None,
    collection_name: Optional[str] = None,
) -> ChromaVectorStore:
    """
    创建向量存储实例

    Args:
        embeddings: 嵌入服务
        persist_directory: 持久化目录
        collection_name: 集合名称

    Returns:
        向量存储实例
    """
    return ChromaVectorStore(
        embeddings=embeddings,
        persist_directory=persist_directory,
        collection_name=collection_name,
    )


def create_vector_store_manager(
    embeddings: Optional[Embeddings] = None,
    persist_directory: Optional[str] = None,
    collection_name: Optional[str] = None,
) -> VectorStoreManager:
    """
    创建向量存储管理器

    Args:
        embeddings: 嵌入服务
        persist_directory: 持久化目录
        collection_name: 集合名称

    Returns:
        向量存储管理器
    """
    vector_store = create_vector_store(
        embeddings=embeddings,
        persist_directory=persist_directory,
        collection_name=collection_name,
    )
    return VectorStoreManager(vector_store)
