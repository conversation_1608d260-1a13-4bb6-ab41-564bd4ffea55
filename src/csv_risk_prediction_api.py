"""
基于CSV的风险预测API
替代Excel处理器，提供更高效的CSV文件风险分析
"""

import logging
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime

from .csv_risk_processor import CSVRiskProcessor
from .file_coded_vector_store import FileCodedVectorStore
from .risk_analyzer import RiskAnalyzer
from .batch_risk_analyzer import BatchRiskAnalyzer

logger = logging.getLogger(__name__)


class CSVRiskPredictionAPI:
    """
    CSV风险预测API

    专门处理CSV格式的风控数据，相比Excel版本有以下优势：
    1. 文档数量更少（基于行数分块，不是字符数）
    2. 处理速度更快（无需解析Excel格式）
    3. 向量质量更高（更简洁的数据结构）
    4. 内存占用更少（纯文本处理）
    """

    def __init__(self, rows_per_chunk: int = 100, chunk_overlap_rows: int = 10):
        """
        初始化CSV风险预测API

        Args:
            rows_per_chunk: 每个文档块包含的行数
            chunk_overlap_rows: 分块重叠的行数
        """
        # 初始化组件
        self.csv_processor = CSVRiskProcessor(
            rows_per_chunk=rows_per_chunk, chunk_overlap_rows=chunk_overlap_rows
        )
        self.file_coded_vector_store = FileCodedVectorStore()
        self.risk_analyzer = RiskAnalyzer(
            file_coded_vector_store=self.file_coded_vector_store
        )
        self.batch_analyzer = BatchRiskAnalyzer(
            file_coded_vector_store=self.file_coded_vector_store
        )

        logger.info("✅ CSV风险预测API初始化完成")
        logger.info(f"分块配置: {rows_per_chunk}行/块, 重叠{chunk_overlap_rows}行")

    def upload_csv_and_get_code(self, file_path: str) -> Dict[str, Any]:
        """
        上传CSV文件并获取文件编码

        Args:
            file_path: CSV文件路径

        Returns:
            上传结果，包含文件编码
        """
        try:
            file_path = Path(file_path)

            if not file_path.exists():
                return {"success": False, "error": f"文件不存在: {file_path}"}

            if file_path.suffix.lower() != ".csv":
                return {
                    "success": False,
                    "error": f"文件格式错误，需要CSV文件，当前: {file_path.suffix}",
                }

            logger.info(f"开始上传CSV文件: {file_path}")

            # 验证CSV文件
            validation = self.csv_processor.validate_csv_file(file_path)
            if not validation["is_valid"]:
                return {
                    "success": False,
                    "error": f"CSV文件验证失败: {validation.get('error', '未知错误')}",
                    "validation": validation,
                }

            # 生成文件编码
            file_code = self.csv_processor.generate_file_code(file_path)

            # 创建文档
            documents = self.csv_processor.create_documents_from_csv(
                file_path, file_code
            )

            # 获取文件信息
            file_info = {
                "file_name": file_path.name,
                "file_size": file_path.stat().st_size,
                "upload_time": datetime.now().isoformat(),
                "analysis": validation["analysis"],
            }

            # 存储到向量数据库
            logger.info(f"开始向量化 {len(documents)} 个文档...")
            self.file_coded_vector_store.create_vector_store_for_file(
                file_code, documents, file_info
            )

            result = {
                "success": True,
                "file_code": file_code,
                "document_count": len(documents),
                "file_size": file_path.stat().st_size,
                "file_info": file_info,
                "validation": validation,
            }

            logger.info(f"✅ CSV文件上传成功，文件编码: {file_code}")
            return result

        except Exception as e:
            logger.error(f"上传CSV文件失败: {e}")
            return {"success": False, "error": f"上传失败: {str(e)}"}

    def predict_by_file_code(
        self, file_code: str, use_batch_analysis: bool = False
    ) -> Dict[str, Any]:
        """
        使用文件编码进行风险预测

        Args:
            file_code: 文件编码
            use_batch_analysis: 是否使用多批次分析

        Returns:
            风险预测结果
        """
        try:
            logger.info(f"开始风险预测，文件编码: {file_code}")
            logger.info(
                f"分析策略: {'多批次分析' if use_batch_analysis else '智能选择分析'}"
            )

            # 检查文件编码是否存在
            if file_code not in self.file_coded_vector_store.list_file_codes():
                return {"success": False, "error": f"文件编码不存在: {file_code}"}

            # 选择分析策略
            if use_batch_analysis:
                # 使用多批次分析
                result = self.batch_analyzer.generate_risk_report_by_file_code(
                    file_code
                )
            else:
                # 使用智能选择分析
                result = self.risk_analyzer.generate_risk_report(file_code)

            if result.get("success"):
                # 添加策略信息
                result["analysis_strategy"] = (
                    "multi_batch" if use_batch_analysis else "smart_selection"
                )
                result["data_source"] = "csv"

                logger.info(
                    f"✅ 风险预测完成，违约概率: {result.get('default_probability', 'N/A'):.2%}"
                )

            return result

        except Exception as e:
            logger.error(f"风险预测失败: {e}")
            return {
                "success": False,
                "error": f"风险预测失败: {str(e)}",
                "file_code": file_code,
            }

    def compare_analysis_strategies(self, file_code: str) -> Dict[str, Any]:
        """
        比较智能选择和多批次分析策略

        Args:
            file_code: 文件编码

        Returns:
            比较结果
        """
        try:
            logger.info(f"开始策略比较分析，文件编码: {file_code}")

            # 智能选择分析
            smart_result = self.predict_by_file_code(
                file_code, use_batch_analysis=False
            )

            # 多批次分析
            batch_result = self.predict_by_file_code(file_code, use_batch_analysis=True)

            # 生成比较报告
            comparison = self._generate_comparison_report(smart_result, batch_result)

            return {
                "success": True,
                "file_code": file_code,
                "smart_selection_result": smart_result,
                "multi_batch_result": batch_result,
                "comparison": comparison,
                "data_source": "csv",
            }

        except Exception as e:
            logger.error(f"策略比较失败: {e}")
            return {"success": False, "error": f"策略比较失败: {str(e)}"}

    def _generate_comparison_report(
        self, smart_result: Dict[str, Any], batch_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成策略比较报告"""
        comparison = {
            "performance_comparison": {},
            "risk_assessment_comparison": {},
            "recommendations": [],
        }

        if smart_result.get("success") and batch_result.get("success"):
            # 性能比较
            comparison["performance_comparison"] = {
                "smart_selection": {
                    "documents_used": smart_result.get("document_count", "unknown"),
                    "analysis_speed": "快速",
                    "data_coverage": "重点覆盖",
                    "suitable_for": "快速评估、日常筛查",
                },
                "multi_batch": {
                    "documents_used": f"全部文档分{batch_result.get('total_batches', 'unknown')}批处理",
                    "analysis_speed": "较慢",
                    "data_coverage": "全面覆盖",
                    "suitable_for": "详细评估、重要决策",
                },
            }

            # 风险评估比较
            smart_prob = smart_result.get("default_probability", 0)
            batch_prob = batch_result.get("default_probability", 0)

            comparison["risk_assessment_comparison"] = {
                "default_probability": {
                    "smart_selection": smart_prob,
                    "multi_batch": batch_prob,
                    "difference": abs(smart_prob - batch_prob),
                    "consistency": (
                        "高"
                        if abs(smart_prob - batch_prob) < 0.1
                        else "中" if abs(smart_prob - batch_prob) < 0.2 else "低"
                    ),
                },
                "risk_level": {
                    "smart_selection": smart_result.get("risk_level", ""),
                    "multi_batch": batch_result.get("risk_level", ""),
                    "consistent": smart_result.get("risk_level")
                    == batch_result.get("risk_level"),
                },
            }

            # 生成建议
            prob_diff = abs(smart_prob - batch_prob)
            if prob_diff < 0.05:
                comparison["recommendations"].append(
                    "两种策略结果高度一致，可使用智能选择策略进行快速评估"
                )
            elif prob_diff < 0.15:
                comparison["recommendations"].append(
                    "两种策略结果基本一致，建议根据时间要求选择策略"
                )
            else:
                comparison["recommendations"].append(
                    "两种策略结果存在较大差异，建议使用多批次策略进行详细分析"
                )

        return comparison

    def list_available_files(self) -> List[Dict[str, Any]]:
        """列出所有可用的CSV文件"""
        try:
            file_codes = self.file_coded_vector_store.list_file_codes()
            files = []

            for file_code in file_codes:
                file_info = self.file_coded_vector_store.get_file_info_by_code(
                    file_code
                )
                if file_info:
                    files.append(
                        {
                            "file_code": file_code,
                            "file_name": file_info.get("file_info", {}).get(
                                "file_name", "unknown"
                            ),
                            "upload_time": file_info.get("file_info", {}).get(
                                "upload_time", "unknown"
                            ),
                            "document_count": len(
                                self.file_coded_vector_store.get_all_documents_by_code(
                                    file_code
                                )
                            ),
                            "data_source": "csv",
                        }
                    )

            return files

        except Exception as e:
            logger.error(f"获取文件列表失败: {e}")
            return []

    def remove_file(self, file_code: str) -> Dict[str, Any]:
        """删除指定的CSV文件数据"""
        try:
            if file_code not in self.file_coded_vector_store.list_file_codes():
                return {"success": False, "error": f"文件编码不存在: {file_code}"}

            self.file_coded_vector_store.remove_file_code(file_code)

            return {"success": True, "message": f"文件 {file_code} 已删除"}

        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return {"success": False, "error": f"删除失败: {str(e)}"}

    def get_file_info(self, file_code: str) -> Dict[str, Any]:
        """获取文件详细信息"""
        try:
            if file_code not in self.file_coded_vector_store.list_file_codes():
                return {"success": False, "error": f"文件编码不存在: {file_code}"}

            file_info = self.file_coded_vector_store.get_file_info_by_code(file_code)
            documents = self.file_coded_vector_store.get_all_documents_by_code(
                file_code
            )

            return {
                "success": True,
                "file_code": file_code,
                "file_info": file_info,
                "document_count": len(documents),
                "data_source": "csv",
            }

        except Exception as e:
            logger.error(f"获取文件信息失败: {e}")
            return {"success": False, "error": f"获取信息失败: {str(e)}"}
