"""
风险预测系统测试
"""

import os
import sys
from pathlib import Path
import tempfile
import pandas as pd

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.file_coded_vector_store import FileCodedVectorStore
from src.excel_risk_processor import ExcelRiskProcessor
from src.file_code_manager import FileCodeManager


def create_test_excel_file() -> str:
    """创建测试用的Excel文件"""
    # 创建临时Excel文件
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as tmp_file:
        # 创建测试数据
        data = {
            '姓名': ['张三'],
            '年龄': [30],
            '月收入': [8000],
            '工作年限': [5],
            '现有负债': [50000],
            '信用评分': [720],
            '教育水平': ['本科'],
            '婚姻状况': ['已婚'],
            '贷款金额': [200000],
            '贷款用途': ['购房']
        }
        
        df = pd.DataFrame(data)
        df.to_excel(tmp_file.name, index=False)
        return tmp_file.name


def test_excel_processor():
    """测试Excel处理器"""
    print("🧪 测试Excel处理器...")
    
    # 创建测试文件
    test_file = create_test_excel_file()
    
    try:
        processor = ExcelRiskProcessor()
        
        # 测试文件验证
        assert processor.validate_excel_file(test_file), "Excel文件验证失败"
        print("✅ Excel文件验证通过")
        
        # 测试文件处理
        file_code, documents, file_info = processor.process_excel_file(test_file)
        
        assert file_code, "文件编码生成失败"
        assert documents, "文档创建失败"
        assert file_info, "文件信息生成失败"
        
        print(f"✅ Excel处理成功，文件编码: {file_code}")
        print(f"   文档数量: {len(documents)}")
        
    finally:
        # 清理测试文件
        os.unlink(test_file)


def test_file_coded_vector_store():
    """测试文件编码向量存储"""
    print("\n🧪 测试文件编码向量存储...")
    
    # 创建测试文件
    test_file = create_test_excel_file()
    
    try:
        # 创建临时存储目录
        with tempfile.TemporaryDirectory() as temp_dir:
            vector_store = FileCodedVectorStore(base_persist_dir=temp_dir)
            processor = ExcelRiskProcessor()
            
            # 处理Excel文件
            file_code, documents, file_info = processor.process_excel_file(test_file)
            
            # 创建向量存储
            vs = vector_store.create_vector_store_for_file(file_code, documents, file_info)
            
            assert vs is not None, "向量存储创建失败"
            print(f"✅ 向量存储创建成功")
            
            # 测试文件编码列表
            file_codes = vector_store.list_file_codes()
            assert file_code in file_codes, "文件编码未找到"
            print(f"✅ 文件编码列表正确")
            
            # 测试文档检索
            retrieved_docs = vector_store.get_all_documents_by_code(file_code)
            assert len(retrieved_docs) > 0, "文档检索失败"
            print(f"✅ 文档检索成功，检索到 {len(retrieved_docs)} 个文档")
            
    finally:
        # 清理测试文件
        os.unlink(test_file)


def test_file_code_manager():
    """测试文件编码管理器"""
    print("\n🧪 测试文件编码管理器...")
    
    # 创建测试文件
    test_file = create_test_excel_file()
    
    try:
        # 创建临时存储目录
        with tempfile.TemporaryDirectory() as temp_dir:
            manager = FileCodeManager(base_persist_dir=temp_dir)
            
            # 测试文件上传
            result = manager.upload_excel_and_get_code(test_file)
            
            assert result["success"], f"文件上传失败: {result.get('error')}"
            file_code = result["file_code"]
            print(f"✅ 文件上传成功，文件编码: {file_code}")
            
            # 测试文件列表
            files = manager.list_available_codes()
            assert len(files) > 0, "文件列表为空"
            assert any(f["file_code"] == file_code for f in files), "文件编码未在列表中找到"
            print(f"✅ 文件列表正确，共 {len(files)} 个文件")
            
            # 测试文件信息获取
            info_result = manager.get_file_info_by_code(file_code)
            assert info_result["success"], "获取文件信息失败"
            print(f"✅ 文件信息获取成功")
            
            # 测试文件删除
            delete_result = manager.remove_file_code(file_code)
            assert delete_result["success"], "文件删除失败"
            print(f"✅ 文件删除成功")
            
            # 验证文件已删除
            files_after_delete = manager.list_available_codes()
            assert not any(f["file_code"] == file_code for f in files_after_delete), "文件删除后仍在列表中"
            print(f"✅ 文件删除验证通过")
            
    finally:
        # 清理测试文件
        os.unlink(test_file)


def test_system_integration():
    """测试系统集成"""
    print("\n🧪 测试系统集成...")
    
    # 检查API密钥
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("⚠️  跳过集成测试：未设置 DEEPSEEK_API_KEY")
        return
    
    # 创建测试文件
    test_file = create_test_excel_file()
    
    try:
        from src.risk_prediction_api import RiskPredictionAPI
        
        # 创建临时存储目录
        with tempfile.TemporaryDirectory() as temp_dir:
            api = RiskPredictionAPI(base_persist_dir=temp_dir)
            
            # 测试完整流程
            upload_result = api.upload_excel_and_get_code(test_file)
            assert upload_result["success"], f"文件上传失败: {upload_result.get('error')}"
            
            file_code = upload_result["file_code"]
            print(f"✅ 集成测试 - 文件上传成功: {file_code}")
            
            # 注意：这里不测试实际的AI预测，因为需要API调用
            print("✅ 集成测试通过（跳过AI预测部分）")
            
    except ImportError as e:
        print(f"⚠️  跳过集成测试：导入失败 {e}")
    finally:
        # 清理测试文件
        os.unlink(test_file)


def main():
    """运行所有测试"""
    print("🚀 开始运行风险预测系统测试...")
    
    try:
        test_excel_processor()
        test_file_coded_vector_store()
        test_file_code_manager()
        test_system_integration()
        
        print("\n✅ 所有测试通过！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
