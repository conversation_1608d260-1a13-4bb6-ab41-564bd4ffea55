"""
风险预测系统使用示例
演示如何使用基于文件编码的风险预测功能
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.risk_prediction_api import RiskPredictionAPI
from src.file_code_manager import FileCodeManager


def demo_basic_usage():
    """基本使用示例"""
    print("=" * 60)
    print("🎯 风险预测系统 - 基本使用示例")
    print("=" * 60)
    
    # 1. 创建API实例
    print("\n1. 初始化风险预测API...")
    api = RiskPredictionAPI()
    print("✅ API初始化完成")
    
    # 2. 模拟上传Excel文件（这里使用示例路径）
    print("\n2. 上传Excel文件...")
    # 注意：这里需要替换为实际的Excel文件路径
    excel_file_path = "data/documents/sample_user_data.xlsx"
    
    if not Path(excel_file_path).exists():
        print(f"⚠️  示例文件不存在: {excel_file_path}")
        print("请将您的Excel文件放在 data/documents/ 目录下")
        return
    
    upload_result = api.upload_excel_and_get_code(excel_file_path)
    
    if upload_result["success"]:
        file_code = upload_result["file_code"]
        print(f"✅ 文件上传成功")
        print(f"   文件编码: {file_code}")
        print(f"   文档数量: {upload_result['document_count']}")
        
        # 3. 进行风险预测
        print(f"\n3. 进行风险预测...")
        risk_report = api.predict_by_file_code(file_code)
        
        if risk_report["success"]:
            print(f"✅ 风险预测完成")
            print(f"\n📊 预测结果:")
            print(f"   违约概率: {risk_report['default_probability']:.2%}")
            print(f"   风险等级: {risk_report['risk_level']}")
            print(f"   风险评分: {risk_report['risk_score']:.4f}")
            print(f"\n📝 AI分析摘要:")
            print(f"   {risk_report['analysis_summary'][:200]}...")
        else:
            print(f"❌ 风险预测失败: {risk_report['error']}")
    else:
        print(f"❌ 文件上传失败: {upload_result['error']}")


def demo_file_management():
    """文件管理示例"""
    print("\n" + "=" * 60)
    print("📁 文件管理功能示例")
    print("=" * 60)
    
    # 创建文件管理器
    manager = FileCodeManager()
    
    # 1. 列出所有文件
    print("\n1. 列出所有可用文件...")
    files = manager.list_available_codes()
    
    if files:
        print(f"✅ 找到 {len(files)} 个文件:")
        for i, file_info in enumerate(files[:5], 1):  # 只显示前5个
            print(f"   {i}. {file_info['file_code']} - {file_info.get('file_name', 'unknown')}")
    else:
        print("📝 暂无可用文件")
    
    # 2. 获取统计信息
    print(f"\n2. 系统统计信息...")
    stats = manager.get_statistics()
    print(f"✅ 统计信息:")
    print(f"   总文件数: {stats['total_files']}")
    print(f"   总文档数: {stats['total_documents']}")
    print(f"   总文件大小: {stats['total_file_size'] / 1024 / 1024:.2f} MB")


def demo_batch_prediction():
    """批量预测示例"""
    print("\n" + "=" * 60)
    print("🔄 批量风险预测示例")
    print("=" * 60)
    
    api = RiskPredictionAPI()
    manager = FileCodeManager()
    
    # 获取所有可用文件
    files = manager.list_available_codes()
    
    if not files:
        print("📝 暂无可用文件进行批量预测")
        return
    
    print(f"📋 准备对 {len(files)} 个文件进行批量预测...")
    
    results = []
    for i, file_info in enumerate(files, 1):
        file_code = file_info['file_code']
        file_name = file_info.get('file_name', 'unknown')
        
        print(f"\n{i}. 分析文件: {file_name} ({file_code})")
        
        risk_report = api.predict_by_file_code(file_code)
        
        if risk_report["success"]:
            result = {
                "file_code": file_code,
                "file_name": file_name,
                "default_probability": risk_report['default_probability'],
                "risk_level": risk_report['risk_level'],
                "success": True
            }
            print(f"   ✅ 违约概率: {risk_report['default_probability']:.2%}, 风险等级: {risk_report['risk_level']}")
        else:
            result = {
                "file_code": file_code,
                "file_name": file_name,
                "error": risk_report['error'],
                "success": False
            }
            print(f"   ❌ 分析失败: {risk_report['error']}")
        
        results.append(result)
    
    # 汇总结果
    print(f"\n📊 批量预测汇总:")
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    print(f"   成功: {len(successful)} 个")
    print(f"   失败: {len(failed)} 个")
    
    if successful:
        avg_prob = sum(r['default_probability'] for r in successful) / len(successful)
        print(f"   平均违约概率: {avg_prob:.2%}")
        
        # 按风险等级分组
        risk_groups = {}
        for r in successful:
            level = r['risk_level']
            if level not in risk_groups:
                risk_groups[level] = 0
            risk_groups[level] += 1
        
        print(f"   风险等级分布:")
        for level, count in risk_groups.items():
            print(f"     {level}: {count} 个")


def demo_complete_workflow():
    """完整工作流程示例"""
    print("\n" + "=" * 60)
    print("🔄 完整工作流程示例")
    print("=" * 60)
    
    # 模拟完整的使用流程
    print("这是一个完整的风险预测工作流程示例:")
    print("\n步骤:")
    print("1. 准备Excel文件（包含用户风控特征数据）")
    print("2. 上传文件并获取文件编码")
    print("3. 使用文件编码进行风险预测")
    print("4. 获取详细的风险分析报告")
    print("5. 根据需要管理文件（查看、删除等）")
    
    print(f"\n💡 使用提示:")
    print(f"   - 每个Excel文件代表一个用户的风控数据")
    print(f"   - 文件编码确保数据完全隔离，不会相互影响")
    print(f"   - AI会基于Excel中的特征数据进行违约概率分析")
    print(f"   - 支持批量处理多个用户的风险评估")


def main():
    """主函数"""
    print("🎯 风险预测系统演示程序")
    
    # 检查API密钥
    if not os.getenv("DEEPSEEK_API_KEY"):
        print("⚠️  请先设置环境变量 DEEPSEEK_API_KEY")
        print("   export DEEPSEEK_API_KEY='your_api_key_here'")
        return
    
    try:
        # 运行各种示例
        demo_complete_workflow()
        demo_file_management()
        
        # 如果有文件，运行批量预测示例
        manager = FileCodeManager()
        if manager.list_available_codes():
            demo_batch_prediction()
        
        # 基本使用示例（需要实际文件）
        # demo_basic_usage()
        
        print(f"\n✅ 演示完成！")
        print(f"💡 要开始使用，请运行: python risk_prediction_main.py --help")
        
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")


if __name__ == "__main__":
    main()
