# ChromaDB 遥测警告修复

## 问题描述

用户在运行程序时遇到以下网络连接警告：

```
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=2, read=2, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1017)')': /batch/
INFO:backoff:Backing off send_request(...) for 0.6s (requests.exceptions.SSLError: HTTPSConnectionPool(host='us.i.posthog.com', port=443): Max retries exceeded with url: /batch/ (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1017)'))))
```

## 问题分析

这些警告来自 **ChromaDB 的遥测功能**：
- ChromaDB 默认会向 PostHog (`us.i.posthog.com`) 发送匿名使用统计数据
- 由于网络连接问题（SSL 错误），导致连接失败并产生警告
- 这些警告不影响 ChromaDB 的核心功能，但会干扰用户体验

## 解决方案

通过环境变量禁用 ChromaDB 的遥测功能：

### 1. 环境变量配置

在 `.env` 文件中添加：
```bash
# ChromaDB 配置 - 禁用遥测以避免网络连接警告
ANONYMIZED_TELEMETRY=False
```

### 2. 配置文件更新

在 `config.py` 中添加配置支持：
```python
# ChromaDB 配置
ANONYMIZED_TELEMETRY: bool = False
```

在 `src/utils.py` 的默认配置中也添加相同配置。

### 3. 模板文件更新

更新 `.env.example` 文件，为新用户提供配置模板。

## 修改的文件

1. `.env` - 添加遥测禁用配置
2. `.env.example` - 更新配置模板
3. `config.py` - 添加配置字段支持
4. `src/utils.py` - 更新默认配置

## 预期效果

- ✅ 消除 PostHog 相关的 SSL 连接警告
- ✅ ChromaDB 功能正常工作，但不再发送遥测数据
- ✅ 系统运行更加安静，没有网络连接干扰
- ✅ 提升用户体验，减少无关警告信息

## 验证方法

运行程序并观察是否还有 PostHog 相关的网络连接警告。

## 参考资料

- [ChromaDB 遥测文档](https://docs.trychroma.com/docs/overview/telemetry)
- [相关 GitHub Issue](https://github.com/chroma-core/chroma/issues/1544)
