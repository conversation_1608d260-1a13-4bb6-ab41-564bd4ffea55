# 基于文件编码的风险预测系统开发

## 项目背景
在现有RAG系统基础上，开发基于文件编码的风险预测功能。系统主要根据指定的xlsx文档来推测违约概率，每个xlsx包含一个用户的风控金融数据。

## 核心需求
1. 通过文件编码指定特定xlsx文件
2. 避免其他xlsx文件对分析结果的干扰
3. 输入文件编码，直接输出完整的风险预测结果和分析依据
4. 基于配置的专业风控分析prompts

## 技术方案
采用基于文件编码的隔离式RAG系统：
- 为每个xlsx文件创建独立的向量存储空间
- 完全隔离不同用户数据
- 专门的风险分析器替代问答链
- 直接输出结构化风险报告

## 实施计划

### 第一阶段：核心组件开发
- [x] 步骤1：创建风控分析专用配置模块
- [x] 步骤2：扩展向量存储管理器
- [x] 步骤3：创建风险预测分析器

### 第二阶段：文件处理和分析引擎
- [x] 步骤4：创建Excel风控数据处理器
- [x] 步骤5：创建风险评估引擎（简化为AI分析）

### 第三阶段：统一API接口
- [x] 步骤6：创建风险预测主接口
- [x] 步骤7：创建文件管理接口

### 第四阶段：主程序和配置
- [x] 步骤8：创建主程序入口
- [x] 步骤9：更新配置和示例

## 开发时间
开始时间: 2025-06-20
完成时间: 2025-06-20

## 关键技术点
- 文件编码生成和管理
- 独立向量存储集合
- 基于AI的风控分析（不使用传统风控引擎）
- Excel内容直接作为知识库
- 完全的文件级数据隔离

## 系统特点
1. **完全隔离**: 每个Excel文件创建独立的向量存储，确保用户数据不会相互影响
2. **AI驱动**: 不预设字段映射，让AI直接分析Excel中的风控特征数据
3. **简单易用**: 通过文件编码即可进行风险预测，支持命令行和交互式使用
4. **灵活扩展**: 支持任意格式的Excel风控数据，AI自动适应不同的数据结构

## 主要文件
- `src/risk_prediction_api.py` - 主要API接口
- `src/file_coded_vector_store.py` - 文件编码向量存储管理
- `src/risk_analyzer.py` - AI风险分析器
- `src/excel_risk_processor.py` - Excel数据处理器
- `risk_prediction_main.py` - 主程序入口
- `examples/risk_prediction_demo.py` - 使用示例

## 使用方法
```bash
# 上传文件并预测
python risk_prediction_main.py --upload data/user_data.xlsx

# 根据文件编码预测
python risk_prediction_main.py --predict user_data_20250620_123456_abc123

# 交互式模式
python risk_prediction_main.py --interactive
```
