# RAG 系统配置优化

## 问题描述

用户反馈在使用简单问候语（如"你好"）时，系统会触发不必要的文档检索，导致：

1. **上下文过长警告**：即使简单问候也会检索文档，拼接后超过4000字符限制
2. **性能浪费**：简单对话不需要检索知识库，但系统无条件执行检索
3. **用户体验差**：简单问候应该直接回答，而不是搜索文档

## 用户建议的解决方案

用户提出了两个很好的建议：

1. **可配置的文档检索开关**：允许用户选择是否启用RAG功能
2. **可配置的上下文长度**：4000字符太短，DeepSeek支持64K+上下文

## 实施的优化方案

### 1. 新增配置选项

在 `config.py` 中添加：

```python
# RAG 配置
ENABLE_DOCUMENT_RETRIEVAL: bool = True  # 是否启用文档检索（关闭后变为纯LLM对话）
MAX_CONTEXT_LENGTH: int = 32000  # 最大上下文长度（DeepSeek支持64K+，32K约8K tokens）
```

### 2. RAG 链逻辑优化

在 `src/rag_chain.py` 的 `ask` 方法中：

- **检索开关判断**：当 `ENABLE_DOCUMENT_RETRIEVAL=False` 时，跳过文档检索
- **纯LLM模式**：直接使用DeepSeek回答，不进行向量搜索
- **模式标识**：在返回结果中添加 `mode` 字段标识当前运行模式

### 3. 环境变量支持

在 `.env.example` 中添加配置说明：

```bash
# RAG 系统配置
# ENABLE_DOCUMENT_RETRIEVAL=True  # 是否启用文档检索（False=纯LLM对话，True=RAG模式）
# MAX_CONTEXT_LENGTH=32000        # 最大上下文长度（DeepSeek支持64K+）
```

## 配置选项详解

### ENABLE_DOCUMENT_RETRIEVAL

- **True（默认）**：启用RAG模式，会检索相关文档来回答问题
- **False**：纯LLM模式，直接使用DeepSeek回答，不检索文档

**使用场景：**
- 纯聊天对话：设置为 False
- 知识库问答：设置为 True
- 混合使用：可以动态切换

### MAX_CONTEXT_LENGTH

- **原值**：4000字符（约1K tokens）
- **新值**：32000字符（约8K tokens）
- **最大支持**：DeepSeek支持64K+ tokens

**建议值：**
- 轻量使用：16000字符（4K tokens）
- 标准使用：32000字符（8K tokens）
- 重度使用：64000字符（16K tokens）

## 运行模式说明

系统现在支持三种运行模式：

1. **pure_llm**：纯LLM模式，不检索文档
2. **rag_enabled**：RAG模式，检索到文档并使用
3. **rag_no_docs**：RAG模式，但未找到相关文档

## 修改的文件

1. `config.py` - 添加新配置选项
2. `src/utils.py` - 更新默认配置
3. `src/rag_chain.py` - 添加检索开关逻辑
4. `.env.example` - 更新配置模板

## 预期效果

- ✅ 解决简单问候触发不必要检索的问题
- ✅ 提供灵活的RAG/纯LLM模式切换
- ✅ 支持更长的上下文，充分利用DeepSeek能力
- ✅ 提升系统性能和用户体验
- ✅ 保持向后兼容性（默认启用RAG）

## 使用示例

### 纯LLM模式
```bash
# 在 .env 中设置
ENABLE_DOCUMENT_RETRIEVAL=False
```

### RAG模式（默认）
```bash
# 在 .env 中设置或使用默认值
ENABLE_DOCUMENT_RETRIEVAL=True
MAX_CONTEXT_LENGTH=32000
```

## 验证方法

1. 设置 `ENABLE_DOCUMENT_RETRIEVAL=False`，测试简单问候是否直接回答
2. 设置 `ENABLE_DOCUMENT_RETRIEVAL=True`，测试知识库问答功能
3. 观察返回结果中的 `mode` 字段确认运行模式
